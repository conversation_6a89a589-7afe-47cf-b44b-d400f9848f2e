from fastapi import APIRouter, Request, Response
from helpers.version_helper import get_version
from ratelimit import limiter


health_check_router = APIRouter()


@health_check_router.get("/v1/api/version", tags=["Health Check"])
@limiter.limit("5/minute")
async def index(request: Request):
    return Response(
        content=f"""<?xml version="1.0" encoding="UTF-8"?><response status="OK">
                    <version api="1.0" referral-ai="{get_version()}"/></response>""",
        media_type="application/xml",
    )
