from fastapi import APIRouter, Request
from fastapi import Depends
from auth import verify_token
from ratelimit import limiter
from services.monitoring_service import start_monitoring

monitor_router = APIRouter()


@monitor_router.get("/v1/monitor-errors", tags=["Monitoring"])
@limiter.limit("5/minute")
async def monitor_errors(
    request: Request,
    user: str = Depends(verify_token),
):
    """
    Endpoint to monitor errors and send alerts if necessary.

    Args:
        request: The FastAPI request object.
        user: The authenticated user.

    Returns:
        Dict: A message indicating the monitoring is complete.
    """
    return await start_monitoring(user)
