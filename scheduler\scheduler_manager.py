from fastapi import FastAPI
import logging
from constants import (
    DISABLE_INTERNAL_SCHEDULER,
    ERROR_MONITORING_INTERVAL_SECONDS,
    MISSING_INFO_AGENT_INTERVAL_SECONDS,
    TASK_WORKFLOW_AGENT_INTERVAL_SECONDS,
)
from contextlib import asynccontextmanager
from scheduler import get_scheduler
from services.monitoring_service import start_monitoring
from services.missing_info_agent_service import MissingInfoAgent
from services.task_and_workflow_agent_service import TaskManagementAndWorkflowAgent


@asynccontextmanager
async def lifespan(app: FastAPI):
    if DISABLE_INTERNAL_SCHEDULER == "true":
        logging.info("⏭️ Internal scheduler is disabled, skipping scheduler startup")
        yield
        return

    scheduler = get_scheduler()
    if not scheduler.get_job("monitor_errors"):
        scheduler.add_job(
            start_monitoring,
            "interval",
            seconds=ERROR_MONITORING_INTERVAL_SECONDS,
            id="monitor_errors",
        )

    if not scheduler.get_job("missing_info_agent"):
        scheduler.add_job(
            MissingInfoAgent().start,
            "interval",
            seconds=MISSING_INFO_AGENT_INTERVAL_SECONDS,
            id="missing_info_agent",
        )

    if not scheduler.get_job("task_management_and_workflow_agent"):
        scheduler.add_job(
            TaskManagementAndWorkflowAgent().start,
            "interval",
            seconds=TASK_WORKFLOW_AGENT_INTERVAL_SECONDS,
            id="task_management_and_workflow_agent",
        )

    scheduler.start()
    logging.info("✅ Scheduler started")
    yield
    logging.info("🛑 Shutting down...")
    scheduler.shutdown()
