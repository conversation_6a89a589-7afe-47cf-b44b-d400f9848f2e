from auth import verify_token
from fastapi import APIRouter, Depends, Request
from ratelimit import limiter
from services.missing_info_agent_service import MissingInfoAgent

missing_info_agent_router = APIRouter()


@missing_info_agent_router.get("/v1/missing-info-agent", tags=["Features"])
@limiter.limit("5/minute")
async def missing_info_agent(
    request: Request,
    user: str = Depends(verify_token),
):
    return await MissingInfoAgent().start(user)
