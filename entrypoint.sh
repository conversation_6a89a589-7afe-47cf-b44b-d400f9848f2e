#!/bin/bash
uvicorn main:app --host 0.0.0.0 --port 9000 &
UVICORN_PID=$!

sleep 60

# Health check loop
failures=0
while true; do
    # Check for 200 status code explicitly
    status_code=$(curl -s -o /dev/null -w "%{http_code}" -m 15 http://localhost:9000/v1/api/version)
    if [ "$status_code" != "200" ]; then
        ((failures++))
        echo "Healthcheck failed ($failures/3) - Status code: $status_code"
        
        if [ $failures -ge 3 ]; then
            echo "Three consecutive failures. Restarting service..."
            kill $UVICORN_PID
            exit 1
        fi
    else
        # Reset failure count on success
        failures=0
    fi
    
    sleep 30
done
