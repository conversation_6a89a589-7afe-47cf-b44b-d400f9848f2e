from fastapi import status
from constants import MISSING_FIELDS_TO_LOOK
from utils.db_utils import get_model
from typing import Any, Dict, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, func, or_, and_, select
from sqlalchemy.exc import SQLAlchemyError
from exceptions import CustomError
from models import (
    PatientReferralAI,
    PatientReferral,
    ReferralFile,
    ReferralFileContents,
)
from db import get_db
from db.monitoring import get_agent_last_run


async def get_clients() -> List[str]:
    """
    Get all clients from the database.
    Returns:
        List[str]: List of client names.
    """
    try:
        async with get_db() as db:
            client_records = await db.execute(
                text("SELECT client_id FROM ld_admin.ld_clients;")
            )
            client_records = client_records.fetchall()

        if not client_records:
            raise CustomError(
                message="No client name was found in the db.",
                status_code=500,
            )

        clients = [client_record[0] for client_record in client_records]
        return clients

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def get_all_referrals_with_missing_fields(
    schema_name: str, db: AsyncSession
) -> List[Dict[str, Any]]:
    """
    Get all referrals with missing fields using SQLAlchemy.

    Args:
        schema_name: The schema name to be used.
        db: SQLAlchemy async database session.

    Returns:
        List[Dict[str, Any]]: The result dictionaries.
    """
    try:
        PatientReferralModel = await get_model(PatientReferral, schema_name)
        ReferralFileModel = await get_model(ReferralFile, schema_name)
        ReferralFileContentsModel = await get_model(ReferralFileContents, schema_name)
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)
        where_conditions = []
        for field in MISSING_FIELDS_TO_LOOK:
            where_conditions.append(
                or_(
                    getattr(PatientReferralModel, field) == None,
                    func.length(func.trim(getattr(PatientReferralModel, field))) == 0,
                )
            )
        query = (
            select(
                PatientReferralModel.refer_id,
                PatientReferralModel.patient_id,
                *[
                    getattr(PatientReferralModel, field)
                    for field in MISSING_FIELDS_TO_LOOK
                ],
                ReferralFileContentsModel.id.label("filecontent_id"),
                ReferralFileContentsModel.ocr_data,
            )
            .select_from(PatientReferralModel)
            .join(
                ReferralFileModel,
                PatientReferralModel.refer_id == ReferralFileModel.refer_id,
            )
            .join(
                ReferralFileContentsModel,
                ReferralFileModel.data_id == ReferralFileContentsModel.id,
            )
            .outerjoin(
                PatientReferralAIModel,
                PatientReferralModel.refer_id == PatientReferralAIModel.refer_id,
            )
            .where(
                and_(
                    PatientReferralAIModel.refer_id == None,
                    or_(*where_conditions),
                )
            )
        )
        missing_fields_records = await db.execute(query)
        rows = missing_fields_records.fetchall()
        columns = missing_fields_records.keys()
        referrals_with_missing_fields = [dict(zip(columns, row)) for row in rows]
        return referrals_with_missing_fields
    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def get_all_referrals_with_new_attachments(
    schema_name: str, db: AsyncSession
) -> List[Dict[str, Any]]:
    """
    Get all referrals with new attachments uploaded after the last run of the Retrigger Agent.

    Args:
        schema_name: The schema name to be used.
        db: SQLAlchemy async database session.

    Returns:
        List[Dict[str, Any]]: The result dictionaries.
    """
    try:
        PatientReferralModel = await get_model(PatientReferral, schema_name)
        ReferralFileModel = await get_model(ReferralFile, schema_name)
        ReferralFileContentsModel = await get_model(ReferralFileContents, schema_name)
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

        last_run_timestamp = await get_agent_last_run(
            db, "Retrigger Agent", schema_name
        )

        current_subquery = (
            select(
                PatientReferralModel.refer_id,
                PatientReferralModel.patient_id,
                ReferralFileContentsModel.id.label("filecontent_id"),
                ReferralFileContentsModel.ocr_data,
                ReferralFileContentsModel.created_date,
                PatientReferralAIModel.require_task,
            )
            .select_from(PatientReferralModel)
            .join(
                ReferralFileModel,
                PatientReferralModel.refer_id == ReferralFileModel.refer_id,
            )
            .join(
                ReferralFileContentsModel,
                ReferralFileModel.data_id == ReferralFileContentsModel.id,
            )
            .join(
                PatientReferralAIModel,
                PatientReferralModel.refer_id == PatientReferralAIModel.refer_id,
            )
        ).alias("current")

        query = select(
            current_subquery.c.refer_id,
            current_subquery.c.patient_id,
            current_subquery.c.filecontent_id,
            current_subquery.c.ocr_data,
            current_subquery.c.created_date,
            current_subquery.c.require_task,
        ).select_from(current_subquery)

        if last_run_timestamp:
            query = query.where(
                and_(
                    current_subquery.c.require_task == True,
                    current_subquery.c.created_date > last_run_timestamp,
                )
            )

        new_attachments_records = await db.execute(query)
        rows = new_attachments_records.fetchall()
        columns = new_attachments_records.keys()
        referral_with_new_attachments = [dict(zip(columns, row)) for row in rows]
        return referral_with_new_attachments
    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def insert_referral_extracted_fields(
    refer_id: int,
    patient_id: int,
    api_data_json: Dict[str, Any],
    schema_name: str,
    require_task: bool = False,
    task_created: bool = False,
    db: AsyncSession = None,
) -> Dict[str, Any]:
    """
    Insert extracted API fields into the referral table using SQLAlchemy.

    Args:
        refer_id: The refer_id of the referral.
        patient_id: The patient_id of the referral.
        api_data_json: The extracted data from the API.
        schema_name: The schema name to be used.
        require_task: Whether a manual task is required (default False).
        task_created: Whether the task has already been created (default False).
        db: SQLAlchemy async database session.

    Returns:
        dict: The status of the insert operation.
    """
    try:
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

        new_record = PatientReferralAIModel(
            refer_id=refer_id,
            patient_id=patient_id,
            res_fields_json=api_data_json,
            require_task=require_task,
            task_created=task_created,
        )

        db.add(new_record)
        await db.commit()

        return {
            "refer_id": refer_id,
            "status": "INSERTED",
            "message": "Record inserted successfully.",
            "api_data_json": api_data_json,
            "require_task": require_task,
            "task_created": task_created,
        }

    except SQLAlchemyError as e:
        await db.rollback()
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def update_referral_extracted_fields(
    refer_id: int,
    api_data_json: Dict[str, Any],
    schema_name: str,
    db: AsyncSession,
) -> Dict[str, Any]:
    """
    Updates specific keys in the res_fields_json column of gs_patient_referral_ai for a given refer_id using SQLAlchemy.

    Args:
        refer_id (int): The refer_id to match.
        api_data_json (Dict[str, Any]): Dictionary of keys/values to update.
        schema_name (str): The client schema name.
        db: SQLAlchemy async database session.

    Returns:
        Dict[str, Any]: Status of the update operation.
    """
    try:
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

        stmt = select(PatientReferralAIModel).where(
            PatientReferralAIModel.refer_id == refer_id
        )
        patient_referral_ai_record = await db.execute(stmt)
        patient_referral_ai_record = patient_referral_ai_record.scalar_one_or_none()

        if not patient_referral_ai_record:
            return {
                "refer_id": refer_id,
                "status": "NOT_FOUND",
                "message": "Record not found.",
            }

        if patient_referral_ai_record.res_fields_json is None:
            patient_referral_ai_record.res_fields_json = api_data_json
        else:
            patient_referral_ai_record.res_fields_json.update(api_data_json)

        await db.commit()

        return {
            "refer_id": refer_id,
            "status": "UPDATED",
            "message": "Record updated successfully.",
            "api_data_json": api_data_json,
        }

    except SQLAlchemyError as e:
        await db.rollback()
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def update_task_creation_status(
    refer_id: int, schema_name: str, db: AsyncSession
) -> Dict[str, Any]:
    """
    Check if all fields in res_fields_json are filled. If yes, update require_task to False using SQLAlchemy.

    Args:
        refer_id: Referral ID to check.
        schema_name: The schema name in the database.
        db: SQLAlchemy async database session.

    Returns:
        dict: Result of the operation.
    """
    try:
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

        stmt = select(PatientReferralAIModel).where(
            PatientReferralAIModel.refer_id == refer_id
        )
        patient_referral_ai_record = await db.execute(stmt)
        patient_referral_ai_record = patient_referral_ai_record.scalar_one_or_none()

        if not patient_referral_ai_record:
            return {"refer_id": refer_id, "status": "NOT_FOUND"}

        res_fields_json = patient_referral_ai_record.res_fields_json

        has_missing = (
            any(
                value is None or str(value).strip() == ""
                for value in res_fields_json.values()
            )
            if res_fields_json
            else True
        )

        if not has_missing:
            patient_referral_ai_record.require_task = False
            await db.commit()
            status = "UPDATED_REQUIRE_TASK_FALSE"
        else:
            status = "FIELDS_MISSING_NO_UPDATE"

        return {"refer_id": refer_id, "status": status}

    except SQLAlchemyError as e:
        await db.rollback()
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        await db.rollback()
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )


async def get_empty_data_points(
    refer_id: int, schema_name: str, db: AsyncSession
) -> List[str]:
    """
    Get the missing datapoints from the res_fields_json column that are empty or None using SQLAlchemy.

    Args:
        refer_id: The refer_id of the referral.
        schema_name: The schema name to be used.
        db: SQLAlchemy async database session.

    Returns:
        List[str]: The list of empty keys in the res_fields_json column.
    """
    try:
        PatientReferralAIModel = await get_model(PatientReferralAI, schema_name)

        stmt = select(PatientReferralAIModel).where(
            PatientReferralAIModel.refer_id == refer_id
        )
        patient_referral_ai_record = await db.execute(stmt)
        patient_referral_ai_record = patient_referral_ai_record.scalar_one_or_none()

        if (
            not patient_referral_ai_record
            or not patient_referral_ai_record.res_fields_json
        ):
            return []

        empty_keys = [
            key
            for key, value in patient_referral_ai_record.res_fields_json.items()
            if value is None or value == ""
        ]

        return empty_keys

    except SQLAlchemyError as e:
        raise CustomError(f"Database error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        raise CustomError(
            f"Unexpected error: {e}", status.HTTP_500_INTERNAL_SERVER_ERROR
        )
