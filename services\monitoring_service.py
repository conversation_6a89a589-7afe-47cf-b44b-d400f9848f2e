from fastapi import status
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>, timezone, datetime
from typing import Dict, <PERSON><PERSON>, List
from sqlalchemy.ext.asyncio import AsyncSession
from db.monitoring import (
    get_monitoring_details,
    get_errors,
    update_monitoring_details,
)
from notification import send_email
from constants import EMAIL_TO_USER, ERROR_ALERT_INTERVAL_MINUTES, ERROR_ALERT_SUBJECT
from helpers.json_parse_helper import str_to_datetime
import logging
from exceptions import CustomError
from db.database import get_db


@dataclass
class ErrorData:
    interface_name: str
    event_id: str
    client_id: str
    error_type: str
    error_details: str
    error_start_time: str
    error_last_time: str
    error_count: int

    def generate_error_key(self) -> str:
        """Generate a unique key for the error."""
        return f"{self.interface_name}__{self.event_id}__{self.client_id}__{self.error_type}__{self.error_details}"

    @staticmethod
    def get_key_text(key) -> str:
        key_details = key.title().split("__")
        return f"\tInterface: {key_details[0]}\n\tEvent: {key_details[1]}\n\tClient: {key_details[2]}\n\tError Type: {key_details[3]}\n\tError Details: {key_details[4]}"

    def to_dict(self, current_time: str) -> Dict:
        """Convert error data to dictionary format."""
        return {
            "alerted_to": EMAIL_TO_USER,
            "alerted_at": current_time,
            "error_start_time": self.error_start_time,
            "error_last_time": self.error_last_time,
            "error_count": self.error_count,
            "new_error": True,
        }


class ErrorMonitor:
    def __init__(self, user: str, current_time: datetime, db: AsyncSession):
        self.user = user
        self.current_time = current_time
        self.db = db
        self.previous_errors = {}
        self.last_run_at = None
        self.new_errors = {}
        self.errors_to_be_alerted = {}

    async def initialize(self):
        """Initialize the monitor by fetching previous error details."""
        self.previous_errors, self.last_run_at = (
            await self._get_last_run_error_details()
        )
        return self

    async def process_errors(self):
        """Main method to process and monitor errors."""
        current_errors = await get_errors(self.db, self.last_run_at)
        self._process_current_errors(current_errors)
        await self._send_error_alerts()
        await self._update_monitoring_state()

    def _process_current_errors(self, current_errors: List[Tuple]):
        """Process each error from the current error list."""
        for error in current_errors:
            error_data = ErrorData(*error)
            error_key = error_data.generate_error_key()
            self._handle_error(error_key, error_data)

    def _handle_error(self, error_key: str, error_data: ErrorData):
        """Handle individual error processing logic."""
        if error_key not in self.previous_errors:
            self._handle_new_error(error_key, error_data)
        else:
            self._handle_existing_error(error_key, error_data)

    def _handle_new_error(self, error_key: str, error_data: ErrorData):
        """Process new errors that haven't been seen before."""
        error_obj = error_data.to_dict(self.current_time)
        self.errors_to_be_alerted[error_key] = error_obj
        self.new_errors[error_key] = error_obj

    def _handle_existing_error(self, error_key: str, error_data: ErrorData):
        """Process errors that have been seen before."""
        error_obj = {**self.previous_errors[error_key]}
        error_obj.update(
            {
                "error_last_time": error_data.error_last_time,
                "error_count": error_obj["error_count"] + error_data.error_count,
                "new_error": False,
            }
        )

        if self._should_alert(error_obj):
            self._update_alert_info(error_key, error_obj)

        self.new_errors[error_key] = error_obj

    def _should_alert(self, error_obj: Dict) -> bool:
        """Determine if an alert should be sent for the error."""
        alert_threshold = self.current_time - timedelta(
            minutes=ERROR_ALERT_INTERVAL_MINUTES
        )
        return str_to_datetime(error_obj["alerted_at"]) < alert_threshold

    def _update_alert_info(self, error_key: str, error_obj: Dict):
        """Update alert information for an error."""
        error_obj["alerted_to"] = EMAIL_TO_USER
        error_obj["alerted_at"] = self.current_time
        self.errors_to_be_alerted[error_key] = error_obj

    async def _send_error_alerts(self):
        """Send email alerts for collected errors."""
        if self.errors_to_be_alerted:
            formatted_errors = ErrorFormatter.format_json(self.errors_to_be_alerted)
            logging.info(
                f"Sending error alerts | Subject: {ERROR_ALERT_SUBJECT} | To: {EMAIL_TO_USER}"
            )
            await send_email(ERROR_ALERT_SUBJECT, formatted_errors, EMAIL_TO_USER)

    async def _update_monitoring_state(self):
        """Update the monitoring state with filtered errors."""
        filtered_errors = await self._filter_errors()
        await update_monitoring_details(
            self.db,
            EMAIL_TO_USER,
            filtered_errors,
            len(self.errors_to_be_alerted) > 0,
            self.current_time,
        )

    async def _get_last_run_error_details(self) -> Tuple[Dict, datetime]:
        """Retrieve details from the last monitoring run."""
        errors, last_run_at = await get_monitoring_details(self.db)
        if errors:
            return errors, last_run_at
        logging.info(
            "No previous monitoring details found. Fetching errors from last 30 minutes"
        )
        return {}, self.current_time - timedelta(minutes=30)

    async def _filter_errors(self) -> Dict:
        """Filter errors based on the alert interval."""
        threshold_time = self.current_time - timedelta(
            minutes=ERROR_ALERT_INTERVAL_MINUTES
        )
        filtered_errors = {
            error_key: error_obj
            for error_key, error_obj in self.previous_errors.items()
            if str_to_datetime(error_obj["error_last_time"]) >= threshold_time
        }
        return filtered_errors | self.new_errors


class ErrorFormatter:
    @staticmethod
    def format_json(json_data: Dict) -> str:
        """Format error data for email content."""
        formatted_errors = []
        for i, key in enumerate(json_data.keys()):
            formatted_errors.append(f"Error {i+1}\n{ErrorData.get_key_text(key)}")
        return "\n\n".join(formatted_errors)


async def monitor_errors_and_alert(user: str, current_time: datetime, db: AsyncSession):
    """Main entry point for error monitoring and alerting."""
    monitor = await ErrorMonitor(user, current_time, db).initialize()
    await monitor.process_errors()


async def start_monitoring(user="Scheduler"):
    """
    Start the monitoring process.

    Args:
        user: The user initiating the monitoring.
        db: SQLAlchemy async database session.

    Returns:
        Dict: A message indicating the monitoring is complete.
    """
    current_time = datetime.now(timezone.utc)
    try:
        async with get_db() as db:
            await monitor_errors_and_alert(user, current_time, db)
    except Exception as e:
        logging.error(f"Error while running monitoring task at {current_time}| {e}")
        raise CustomError(
            f"Error while running monitoring task at {current_time} | {e}",
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
    return {
        "message": "Error monitoring completed.",
    }
