from fastapi import APIRouter, Request, Response
from helpers.version_helper import get_version
from ratelimit import limiter


index_router = APIRouter()


@index_router.get("/", tags=["Index"], include_in_schema=False)
@limiter.limit("5/minute")
async def index(request: Request):
    return {
        "Status": True,
        "message": "Referral AI API is working..",
        "version": get_version(),
    }


@index_router.get(
    "/.well-known/appspecific/com.chrome.devtools.json",
    response_class=Response,
    include_in_schema=False,
)
@limiter.limit("5/minute")
def chrome_devtools_xml(request: Request):
    return Response(
        content=f"""<?xml version="1.0" encoding="UTF-8"?><response status="OK">
                    <version api="1.0" referral-ai="{get_version()}"/></response>""",
        media_type="application/xml",
    )
