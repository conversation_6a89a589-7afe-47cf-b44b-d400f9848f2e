from fastapi import APIRouter, Depends, Request
from ratelimit import limiter
from models import OAuth2LoginRequestForm, Token
from auth import (
    create_access_token,
    get_auth_token,
    verify_credentials,
    verify_refresh_token,
    verify_user,
)
from exceptions import UnauthorizedError


login_router = APIRouter()


@login_router.post("/v1/token", response_model=Token, tags=["Auth"])
@limiter.limit("5/minute")
async def login(request: Request, auth_form: OAuth2LoginRequestForm = Depends()):
    if auth_form.grant_type == "refresh_token":
        payload = await verify_refresh_token(
            auth_form.refresh_token, auth_form.grant_type
        )
        username = payload.get("user")
        await verify_user(username)

        access_token = await create_access_token({"user": username})
        return {"access_token": access_token, "token_type": "bearer"}
    elif auth_form.grant_type == "password":
        await verify_credentials(auth_form)
        return await get_auth_token({"user": auth_form.username})
    else:
        raise UnauthorizedError("Invalid grant type!")
