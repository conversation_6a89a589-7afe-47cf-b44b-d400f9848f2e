import aiosmtplib
from email.message import EmailMessage
from constants import EMAIL_FROM_USER
import logging
from exceptions import CustomError
import ssl


async def send_email(subject, body, email_id, server="localhost"):
    msg = EmailMessage()
    msg["From"] = EMAIL_FROM_USER
    msg["To"] = email_id
    msg["Subject"] = subject

    msg.set_content(body)
    try:
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        async with aiosmtplib.SMTP(
            hostname=server, port=25, start_tls=True, tls_context=ssl_context
        ) as smtp:
            await smtp.send_message(msg)
            logging.info(f"Email sent to {email_id} successfully!")
    except aiosmtplib.SMTPAuthenticationError as auth_error:
        error_msg = f"Email authentication failed: {auth_error}"
        logging.error(error_msg)
        raise CustomError(error_msg)
    except aiosmtplib.SMTPException as send_error:
        error_msg = f"Failed to send email to {email_id}: {send_error}"
        logging.error(error_msg)
        raise CustomError(error_msg)
    except (ssl.SSLError, ConnectionError, TimeoutError) as conn_error:
        error_msg = f"Email server connection error: {conn_error}"
        logging.error(error_msg)
        raise CustomError(error_msg)
    except Exception as e:
        error_msg = f"Unexpected error sending email to {email_id}: {e}"
        logging.error(error_msg)
        raise CustomError(error_msg)
