from fastapi import APIRouter, Depends, Request
from ratelimit import limiter
from auth import verify_token
from services.task_and_workflow_agent_service import TaskManagementAndWorkflowAgent

task_management_and_workflow_agent_router = APIRouter()


@task_management_and_workflow_agent_router.get(
    "/v1/task-management-and-workflow-agent", tags=["Features"]
)
@limiter.limit("5/minute")
async def task_management_and_workflow_agent(
    request: Request,
    user: str = Depends(verify_token),
):
    return await TaskManagementAndWorkflowAgent().start(user)
