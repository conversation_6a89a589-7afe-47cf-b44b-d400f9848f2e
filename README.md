# Referral AI

Referral AI is an application designed to handle AI-related tasks for LincWare. This repository contains the source code for the Referral AI API, which includes authentication, secure data handling, and agent operations.

## Table of Contents

- [Installation](#installation)
- [Usage](#usage)
- [API Endpoints](#api-endpoints)
- [Agents](#agents)

## Installation

1. Clone the repository:
    ```sh
    git clone https://github.com/lincware/referral-ai.git
    cd referral-ai
    ```

2. Create a virtual environment and activate it:
    ```sh
    python -m venv venv
    .\venv\Scripts\activate  # On Windows
    source venv/bin/activate  # On Unix or MacOS
    ```

3. Install the dependencies:
    ```sh
    pip install -r requirements.txt
    ```

4. Set up the environment variables.

## Usage

1. Run the application:
    ```sh
    uvicorn main:app --host 0.0.0.0 --port 9000
    ```

2. Access the API documentation at `http://localhost:9000/docs`.

## API Endpoints

### Authentication

- `POST /v1/login`: Login endpoint for user authentication.

### Features

- `GET /v1/secure-data`: Secure endpoint to get database version.
- `GET /v1/missing-info-agent`: Endpoint to start the missing info agent.

### Index

- `GET /`: Health check endpoint to verify if the API is working.

## Agents

### 1. Missing Info & Validation Agent
![Alt text](images/Referral-Intake-&-Validation-Agent.png)

### 2. Task Management & Workflow Agent
![Alt text](images/Task-Management-and-Workflow-Agent.png)